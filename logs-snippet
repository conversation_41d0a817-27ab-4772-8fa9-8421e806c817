Guest dashboard configuration:
guest:1215 - User ID: temp_magic_
guest:1216 - Property ID: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest:1217 - WebSocket URL: http://localhost:8086/
guest:1218 - WebSocket API URL: http://localhost:8086/
guest:1219 - Guest Name: Guest
guest:1220 - Phone Number: ***-***-9881
guest:1221 - Secure credentials will be loaded via API endpoints
guest:2263 Initialized reservations data: [{…}]
guest:1137 Loading Firebase configuration from secure endpoint...
auth.js:358 Checking authentication state...
auth.js:10 Initializing Firebase securely for auth...
resource-loader.js:89 Firebase already loaded, skipping load from resource-loader
guest_dashboard_main.js:600 === GUEST DASHBOARD INITIALIZATION ===
guest_dashboard_main.js:601 DOM Content Loaded at: 2025-08-04T21:04:39.541Z
guest_dashboard_utils.js:956 Initializing dashboard state from template data
guest_dashboard_utils.js:982 Initialized dashboard state with reservations from template
guest_dashboard_utils.js:983 Dashboard state initialized: {propertyId: 'dcb5f05d-0a68-40ba-b314-06ed18121a7c', guestName: 'Guest', phoneNumber: '***-***-9881', reservationsCount: 1}
guest_dashboard_main.js:614 Found reservations in template data, checking for guest info: 1
guest_dashboard_main.js:715 User ID: temp_magic_
guest_dashboard_main.js:716 Guest name: Guest
guest_dashboard_main.js:717 Phone number: Available
guest_dashboard_main.js:736 updateGuestNameDisplay function is properly defined
guest_dashboard_main.js:1866 Updating guest name display...
guest_dashboard_main.js:1882 Using guest name from dashboard state: "Guest" (source: unknown)
guest_dashboard_main.js:744 Waiting for Firebase to initialize securely...
guest_dashboard_voice_call.js:119 Using Gemini voice: Aoede
guest_dashboard_voice_call.js:120 Using Gemini language: en-US
guest_dashboard_utils.js:899 updateGuestName called with name=Guest, source=magic_link
guest_dashboard_utils.js:926 Updating guest name to "Guest" from source: magic_link
guest_dashboard_utils.js:939 Directly updated DOM element guest-name to "Guest"
guest_dashboard_utils.js:948 Guest name updated successfully to "Guest" from source: magic_link
guest_dashboard_main.js:689 Initialized guest name: "Guest" from source: magic_link
guest:1157 Firebase configuration loaded securely
guest:1162 Firebase initialized securely
auth.js:13 Firebase auth initialized securely
guest_dashboard_main.js:746 Firebase initialized successfully
guest_dashboard_main.js:1020 Firebase already initialized, using existing instance
guest_dashboard_text_chat.js:292 initializeSocketIOProcess called
guest_dashboard_text_chat.js:296 Temporary user detected, using temporary ID token
guest_dashboard_text_chat.js:297 Temporary user token available: Yes
guest_dashboard_text_chat.js:298 Temporary user flag: true
guest_dashboard_text_chat.js:304 Stored temporary token in dashboardState and window.storedIdToken
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_text_chat.js:1086 initializeChat called
guest_dashboard_text_chat.js:1096 Chat UI elements found:
guest_dashboard_text_chat.js:1097 - chatInput: Found
guest_dashboard_text_chat.js:1098 - sendMessageButton: Found
guest_dashboard_text_chat.js:1015 Chat connection status: Disconnected
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_text_chat.js:1117 Setting up speech recognition...
guest_dashboard_text_chat.js:210 Web Speech API supported and initialized.
guest_dashboard_text_chat.js:1125 Speech recognition enabled
guest_dashboard_main.js:880 Voice selector not found in the DOM
guest_dashboard_main.js:933 === CHECKING RESERVATION LOADING CONDITION ===
guest_dashboard_main.js:934 reservationsLoaded: undefined
guest_dashboard_main.js:935 isLoadingReservations: undefined
guest_dashboard_main.js:936 CURRENT_USER_ID: temp_magic_
guest_dashboard_main.js:944 First time loading reservations
guest_dashboard_main.js:953 Loading reservations using imported function
guest_dashboard_reservations.js:23 Loading reservations...
guest_dashboard_reservations.js:57 Using user ID for reservation lookup: temp_magic_
guest_dashboard_reservations.js:63 Calling reservations API endpoint: /api/reservations/temp_magic_
guest_dashboard_voice_call.js:291 Initializing voice call system...
guest_dashboard_voice_call.js:1065 Voice selector not found in the DOM
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_main.js:1013 Guest dashboard initialization complete
auth.js:377 Auth state changed, user: signed out
auth.js:474 User is signed out of Firebase. Updating UI.
auth.js:488 Current pathname: /magic/TkfzQwX456BFyugZkEGyilJs5JtQuNKuscrS/guest
auth.js:503 User signed out (client-side), but backend session may be valid. No redirect performed to avoid loop.
(anonymous) @ auth.js:503
next @ auth.ts:358
(anonymous) @ auth_impl.ts:600
Promise.then
registerStateListener @ auth_impl.ts:600
onAuthStateChanged @ auth_impl.ts:442
onAuthStateChanged @ auth.ts:180
checkAuthState @ auth.js:376
await in checkAuthState
(anonymous) @ auth.js:737
auth.js:504 Current pathname is not recognized as login page: /magic/TkfzQwX456BFyugZkEGyilJs5JtQuNKuscrS/guest
guest_dashboard_main.js:788 === AUTH STATE CHANGED ===
guest_dashboard_main.js:847 User is signed out
guest_dashboard_main.js:1866 Updating guest name display...
guest_dashboard_main.js:1882 Using guest name from dashboard state: "Guest" (source: magic_link)
guest_dashboard_reservations.js:71 Reservations API response: {reservations: Array(1), success: true}
guest_dashboard_reservations.js:163 Setting current property index to 0
guest_dashboard_reservations.js:180 Keeping existing property ID: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_reservations.js:185 Prefetching details for 1 properties
guest_dashboard_utils.js:209 Fetching property details for dcb5f05d-0a68-40ba-b314-06ed18121a7c from API
guest_dashboard_voice_call.js:2249 === CHECK VOICE CALL READINESS ===
guest_dashboard_voice_call.js:2250 propertyReady: true, propertyId: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_voice_call.js:2251 userIdReady: true, userId: temp_magic_
guest_dashboard_voice_call.js:2252 currentCallState: idle
guest_dashboard_voice_call.js:2256 Voice call is ready, enabling button
guest_dashboard_utils.js:280 Confirmed property name: Golf View Resort Studio w/Indoor & Outdoor Pool
guest_dashboard_utils.js:281 Confirmed property address: Pigeon Forge, Tennessee, United States
guest_dashboard_reservations.js:799 Property details updated for dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_utils.js:375 Property details updated for dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_reservations.js:324 Rendering reservation cards: [{…}]
guest_dashboard_reservations.js:328 Guest dashboard uses modal-based reservations, skipping main container rendering.
guest_dashboard_reservations.js:639 Updating selected property UI. Current index: 0, Property ID: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:04 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:04:40.547Z -> Mon Aug 04 2025
guest:2410 Inserting date separator: Today
guest_dashboard_voice_call.js:345 Voice call button clicked, current state: idle
guest_dashboard_voice_call.js:348 === PROPERTY ID DEBUG INFO ===
guest_dashboard_voice_call.js:349 confirmedPropertyId (imported): dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_voice_call.js:350 window.PROPERTY_ID: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_voice_call.js:351 document.body.dataset.propertyId: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_voice_call.js:352 window.propertyDetails: Available
guest_dashboard_voice_call.js:365 Requesting ephemeral token for voice call...
guest_dashboard_voice_call.js:1152 Requesting ephemeral token from server...
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:13.280Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:04:40.547Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:1174 Ephemeral token received successfully
guest_dashboard_voice_call.js:368 Ephemeral token obtained successfully
guest_dashboard_voice_call.js:387 Using authentication token: Present (hidden)
guest_dashboard_voice_call.js:396 Final property ID for voice call: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_voice_call.js:401 Microphone access granted.
guest_dashboard_voice_call.js:426 Starting Gemini voice call for property: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_voice_call.js:440 Fetching knowledge items for voice call...
guest_dashboard_utils.js:438 Fetching knowledge items for property dcb5f05d-0a68-40ba-b314-06ed18121a7c from API
guest_dashboard_utils.js:455 Stored raw knowledge items in propertyDetails.knowledgeItems
guest_dashboard_utils.js:468 Retrieved knowledge items from Firestore: 4 items found
guest_dashboard_utils.js:469 Formatted knowledge items stored in window.propertyKnowledgeItems
guest_dashboard_utils.js:473 Sample knowledge items: (3) [{…}, {…}, {…}]
guest_dashboard_voice_call.js:442 Knowledge items fetched successfully for voice call
guest_dashboard_voice_call.js:448 Using property details: Available
guest_dashboard_voice_call.js:449 Property name: Golf View Resort Studio w/Indoor & Outdoor Pool
guest_dashboard_voice_call.js:450 Property address: Pigeon Forge, Tennessee, United States
guest_dashboard_voice_call.js:471 [Deprecation] The ScriptProcessorNode is deprecated. Use AudioWorkletNode instead. (https://bit.ly/audio-worklet)
startGeminiVoiceCall @ guest_dashboard_voice_call.js:471
await in startGeminiVoiceCall
handleVoiceCallClick @ guest_dashboard_voice_call.js:404
guest_dashboard_voice_call.js:501 Ephemeral token received but falling back to API key method for WebSocket connection
guest_dashboard_voice_call.js:1110 Fetching Gemini API key from secure endpoint...
guest_dashboard_voice_call.js:1141 Fetched Gemini API Key successfully from secure endpoint.
guest_dashboard_voice_call.js:506 Attempting WebSocket connection to Gemini Live API with API key: wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=REDACTED_KEY&alt=json
guest_dashboard_voice_call.js:512 Using model: gemini-live-2.5-flash-preview
guest_dashboard_voice_call.js:515 Creating WebSocket connection...
guest_dashboard_voice_call.js:826 Voice call successfully started.
guest_dashboard_voice_call.js:527 WebSocket connection established with Gemini voice API
guest_dashboard_voice_call.js:2313 Creating voice conversation session...
guest_dashboard_voice_call.js:2322 Found reservation ID from current reservation: f9333be9-1f13-4c5e-a437-1e99074b6cfe
guest_dashboard_voice_call.js:2339 Phone number for voice conversation: ***-***-9881
guest_dashboard_voice_call.js:2351 Including reservation ID in voice conversation: f9333be9-1f13-4c5e-a437-1e99074b6cfe
guest_dashboard_voice_call.js:2357 Including phone number in voice conversation: ***-***-9881
guest_dashboard_voice_call.js:1188 [VOICE DEBUG] Current guest name: Guest
guest_dashboard_voice_call.js:1189 [VOICE DEBUG] createSharedSystemPrompt available: function
guest_dashboard_voice_call.js:1193 [VOICE DEBUG] Using shared system prompt function for voice call
guest_dashboard_utils.js:571 [SHARED PROMPT DEBUG] Creating shared system prompt for property: Golf View Resort Studio w/Indoor & Outdoor Pool
guest_dashboard_utils.js:572 [SHARED PROMPT DEBUG] Property details available: Yes
guest_dashboard_utils.js:573 [SHARED PROMPT DEBUG] Knowledge items available: Yes
guest_dashboard_utils.js:574 [SHARED PROMPT DEBUG] Using guest name: Guest
guest_dashboard_utils.js:575 [SHARED PROMPT DEBUG] globalDashboardState.guestName: Guest
guest_dashboard_utils.js:576 [SHARED PROMPT DEBUG] window.GUEST_NAME: Guest
guest_dashboard_utils.js:577 [SHARED PROMPT DEBUG] Guest name is 'Guest': true
guest_dashboard_utils.js:578 [SHARED PROMPT DEBUG] Will include name asking instruction: true
guest_dashboard_utils.js:582 createSharedSystemPrompt: propertyDetails available: {propertyId: 'dcb5f05d-0a68-40ba-b314-06ed18121a7c', name: 'Golf View Resort Studio w/Indoor & Outdoor Pool', address: 'Pigeon Forge, Tennessee, United States', checkInTime: '15:00', checkOutTime: '11:00', …}
guest_dashboard_utils.js:591 createSharedSystemPrompt: Using knowledge items from globalDashboardState, count: 4
guest_dashboard_utils.js:719 Added reservation context to system prompt: 

Reservation Details:
Check-in: Aug 13, 2025
Check-out: Aug 19, 2025

guest_dashboard_utils.js:787 createSharedSystemPrompt: Created system prompt with length: 4841
guest_dashboard_voice_call.js:1195 [VOICE DEBUG] Generated prompt length: 4841
guest_dashboard_voice_call.js:1196 [VOICE DEBUG] Prompt contains guest name handling: true
guest_dashboard_voice_call.js:597 Sending initial configuration to Gemini voice API
guest_dashboard_voice_call.js:598 Using language: en-US for voice call
guest_dashboard_voice_call.js:1828 🔊 Started noise monitoring
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:14.986Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:13.280Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:14.987Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:14.986Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 26)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {setupComplete: {…}}
guest_dashboard_voice_call.js:2061 Gemini setup complete: {}
guest_dashboard_voice_call.js:2374 Voice conversation session created: 4fcce1cd-8655-428a-876a-9d7ff1cb5644
guest_dashboard_voice_call.js:532 Voice conversation session ready: 4fcce1cd-8655-428a-876a-9d7ff1cb5644
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:15.258Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:14.987Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 36)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:614 Sending initial greeting message (new session)
guest_dashboard_voice_call.js:615 Greeting message: Hello, my name is Guest. Please greet me by name very briefly!
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1437 Created audio context with sample rate: 24000Hz
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "Hel"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 10448)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1416 Queueing audio for playback (9600 bytes, ~4800 samples, ~0.20s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "may"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "hel"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 10448)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "p "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 91)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 60)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0001, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:1564 Audio chunk playback complete
2guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 452)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}, usageMetadata: {…}}
guest_dashboard_voice_call.js:2084 Model turn complete
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:18.658Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:15.258Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0001, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 138)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 36)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:2079 Received session resumption handle: Cih2bml6NDB3ZTVyeXhycmEzODYzZTdkMWV2M3ZmMTBpd244bGVhdmkw
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0004, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:2487 📝 AI Voice Complete: Hello Guest. How may I help you today?
guest_dashboard_voice_call.js:2414 Storing assistant voice message: Hello Guest. How may I help you today?
guest_dashboard_voice_call.js:2432 ✅ Successfully stored assistant voice message to DynamoDB
guest_dashboard_voice_call.js:2466 🎯 Displayed assistant voice transcription in chat UI
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:21.875Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:18.658Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0411, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 87)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "Hel"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1416 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 82)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1389 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 10448)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 89)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 60)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "n I"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): " h"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 53)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 450)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2067 User interrupted - clearing audio queue
guest_dashboard_voice_call.js:1801 🚫 Interruption detected (count: 1)
guest_dashboard_voice_call.js:956 🔇 Stopping all audio playback immediately
guest_dashboard_voice_call.js:1007 🗑️ Dropped 4 audio chunks
guest_dashboard_voice_call.js:1010 ✅ All audio playback stopped successfully
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}, usageMetadata: {…}}
guest_dashboard_voice_call.js:2084 Model turn complete
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:24.126Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:21.875Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:24.128Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:24.126Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 138)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 36)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:2079 Received session resumption handle: CihsZDN3aDk4d2hqdGRiNXZ5NGViOG0wNGZsejRydTEzbWZ3dzh6YXBv
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0134, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 82)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 81)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (user): "i"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 90)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (user): " password."
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0171, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 290)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 250)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): " Wi"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "Fi "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "wor"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "or "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1416 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1416 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "iew"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1389 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1389 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1362 Converting base64 to ArrayBuffer (length: 15360)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "ncn"
guest_dashboard_voice_call.js:1543 Scheduled audio chunk (0.240s) to play at 12.655, queue length: 14
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1416 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): ", a"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1543 Scheduled audio chunk (0.240s) to play at 12.895, queue length: 17
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0009, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "ord"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1389 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): " is"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1362 Converting base64 to ArrayBuffer (length: 15360)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): " xf"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1389 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1416 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (ai): "gjc"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1416 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1389 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1627 Audio data analysis (11520 bytes, 5760 samples):
guest_dashboard_voice_call.js:1628 - Min: -5768, Max: 3204, Avg: 62.21
guest_dashboard_voice_call.js:1629 - Zero samples: 0 (0.00%)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 5410)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 60)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1543 Scheduled audio chunk (0.240s) to play at 20.095, queue length: 17
guest_dashboard_voice_call.js:1564 Audio chunk playback complete
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0000, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:2487 📝 User Voice Complete: Hello. Wi-Fi password.
guest_dashboard_voice_call.js:2414 Storing user voice message: Hello. Wi-Fi password.
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:2432 ✅ Successfully stored user voice message to DynamoDB
guest_dashboard_text_chat.js:133 displayChatMessage: Using guest name for user message: Guest (window.GUEST_NAME = Guest )
guest_dashboard_voice_call.js:2466 🎯 Displayed user voice transcription in chat UI
guest_dashboard_text_chat.js:187 Adding date separator for message: user 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:31.813Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:24.128Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:1564 Audio chunk playback complete
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0000, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:2487 📝 AI Voice Complete: Hello Guest. How can I help you?The WiFi network for Golf View Resort Studio is fxhvghncn, and the password is xfgnvhjhgjcty67.
guest_dashboard_voice_call.js:2414 Storing assistant voice message: Hello Guest. How can I help you?The WiFi network f...
guest_dashboard_voice_call.js:2432 ✅ Successfully stored assistant voice message to DynamoDB
guest_dashboard_voice_call.js:2466 🎯 Displayed assistant voice transcription in chat UI
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:34.387Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:31.813Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:1564 Audio chunk playback complete
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0000, threshold=0.15, noisy=false
2guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1564 Audio chunk playback complete
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0000, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:1564 Audio chunk playback complete
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0000, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1564 Audio chunk playback complete
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 452)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}, usageMetadata: {…}}
guest_dashboard_voice_call.js:2084 Model turn complete
guest_dashboard_voice_call.js:1564 Audio chunk playback complete
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:40.914Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:34.387Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0000, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 138)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 36)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:2079 Received session resumption handle: CihlNnp2YjBxZjNzbmh5d3BkMzlwdjBkY3V3NnF1dmN0bGx3MjhiNzky
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0000, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0034, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 85)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (user): " Okay"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 81)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (user): " wha"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0185, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 85)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (user): " name"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 81)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2202 📝 Fragment (user): "?"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 5328)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 82)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1872 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 87)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 60)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0001, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 450)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}, usageMetadata: {…}}
guest_dashboard_voice_call.js:2084 Model turn complete
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:49.606Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:40.914Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 138)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 36)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:2079 Received session resumption handle: CihlM256aXE5MXlvM2hic2s5eWs3dWJuN2VkM3RiaDl6cms4am10YWFy
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0014, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:2487 📝 User Voice Complete: Okay, what is my name?
guest_dashboard_voice_call.js:2414 Storing user voice message: Okay, what is my name?
guest_dashboard_voice_call.js:2432 ✅ Successfully stored user voice message to DynamoDB
guest_dashboard_text_chat.js:133 displayChatMessage: Using guest name for user message: Guest (window.GUEST_NAME = Guest )
guest_dashboard_voice_call.js:2466 🎯 Displayed user voice transcription in chat UI
guest_dashboard_text_chat.js:187 Adding date separator for message: user 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:52.317Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:49.606Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:1681 🔊 Noise analysis: avg=0.0013, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:345 Voice call button clicked, current state: active
guest_dashboard_voice_call.js:348 === PROPERTY ID DEBUG INFO ===
guest_dashboard_voice_call.js:349 confirmedPropertyId (imported): dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_voice_call.js:350 window.PROPERTY_ID: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_voice_call.js:351 document.body.dataset.propertyId: dcb5f05d-0a68-40ba-b314-06ed18121a7c
guest_dashboard_voice_call.js:352 window.propertyDetails: Available
guest_dashboard_voice_call.js:419 User initiated call end.
guest_dashboard_voice_call.js:836 Stopping voice call. Reason: User ended call
guest_dashboard_voice_call.js:874 Microphone stream stopped.
guest_dashboard_voice_call.js:956 🔇 Stopping all audio playback immediately
guest_dashboard_voice_call.js:1010 ✅ All audio playback stopped successfully
guest_dashboard_voice_call.js:1844 🔇 Stopped noise monitoring and reset state
guest_dashboard_voice_call.js:935 🎤 AI Complete (final): Your name is Guest.
guest_dashboard_voice_call.js:2487 📝 AI Voice Complete: Your name is Guest.
guest_dashboard_voice_call.js:2414 Storing assistant voice message: Your name is Guest.
guest_dashboard_voice_call.js:2501 Cleaning up voice conversation session: 4fcce1cd-8655-428a-876a-9d7ff1cb5644
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:53.719Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:52.317Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:2432 ✅ Successfully stored assistant voice message to DynamoDB
guest_dashboard_voice_call.js:2466 🎯 Displayed assistant voice transcription in chat UI
guest_dashboard_voice_call.js:712 WebSocket closed. Code: 1000, Reason: 
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 04:05 PM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-04T21:05:53.847Z -> Mon Aug 04 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-04T21:05:53.719Z -> Mon Aug 04 2025
guest:2416 No date separator needed for message
#!/usr/bin/env python3
"""
Final script to analyze the August 2nd conversations by querying the Conversations table directly.
"""

import os
import sys
import boto3
import argparse
from datetime import datetime, timezone, timedelta
from boto3.dynamodb.conditions import Key, Attr
from typing import Dict, List, Optional
import json

def setup_dynamodb():
    """Initialize DynamoDB connection."""
    try:
        # Initialize DynamoDB resource with correct region
        dynamodb = boto3.resource('dynamodb', region_name='us-east-2')
        
        # Get table name from environment or use default
        table_name = os.environ.get('CONVERSATIONS_TABLE_NAME', 'Conversations')
        
        conversations_table = dynamodb.Table(table_name)
        
        print(f"✓ Connected to DynamoDB table: {table_name}")
        return conversations_table
    except Exception as e:
        print(f"✗ Error connecting to DynamoDB: {e}")
        return None

def get_conversation_details(conversation_id, property_id, table):
    """Get detailed information about a specific conversation."""
    print(f"\n🔍 DETAILED ANALYSIS FOR CONVERSATION")
    print(f"Conversation ID: {conversation_id}")
    print(f"Property ID: {property_id}")
    print("=" * 80)
    
    try:
        # Query for the specific conversation
        response = table.query(
            KeyConditionExpression=Key('PK').eq(f"PROPERTY#{property_id}") &
                                  Key('SK').eq(f"CONVERSATION#{conversation_id}")
        )
        
        items = response.get('Items', [])
        
        if not items:
            print("❌ Conversation not found")
            return None
        
        conversation = items[0]
        
        print(f"📋 CONVERSATION DETAILS:")
        print(f"  Property ID: {conversation.get('PropertyId', 'Unknown')}")
        print(f"  User ID: {conversation.get('UserId', 'Unknown')}")
        print(f"  Guest Name: {conversation.get('GuestName', 'Unknown')}")
        print(f"  Channel: {conversation.get('Channel', 'Unknown')}")
        print(f"  Start Time: {conversation.get('StartTime', 'Unknown')}")
        print(f"  Last Update: {conversation.get('LastUpdateTime', 'Unknown')}")
        print(f"  Message Count: {conversation.get('MessageCount', 0)}")
        print(f"  Reservation ID: {conversation.get('ReservationId', 'None')}")
        print(f"  Phone: {conversation.get('GuestPhone', 'None')}")
        print()
        
        # Get the messages
        messages = conversation.get('Messages', [])
        
        if messages:
            print(f"💬 CONVERSATION MESSAGES ({len(messages)} total):")
            print("-" * 80)
            
            for i, message in enumerate(messages, 1):
                role = message.get('role', 'Unknown')
                text = message.get('text', 'No text')
                timestamp = message.get('timestamp', 'No timestamp')
                
                print(f"{i:2d}. [{timestamp}] {role.upper()}")
                print(f"     Text: {text}")
                
                # Show context used for assistant messages
                if role == 'assistant' and message.get('context_used'):
                    context_used = message.get('context_used', [])
                    print(f"     Context Used: {len(context_used)} items")
                    for j, context_item in enumerate(context_used[:3], 1):  # Show first 3 items
                        context_text = context_item.get('text', 'No content')[:100]
                        print(f"       {j}. {context_text}...")
                    if len(context_used) > 3:
                        print(f"       ... and {len(context_used) - 3} more items")
                
                print()
        else:
            print("❌ No messages found in conversation")
        
        return conversation
        
    except Exception as e:
        print(f"✗ Error analyzing conversation: {e}")
        return None

def analyze_problematic_conversations():
    """Analyze the specific conversations that match the problematic criteria."""
    table = setup_dynamodb()
    if not table:
        sys.exit(1)
    
    # The conversations that match the 3 PM Chicago time criteria
    target_conversations = [
        {
            'id': 'de1305d6-d3f3-4023-84df-e16168f9fcdc',
            'property_id': 'c2d26719-5654-45ee-aa2c-8fbafa637fb4',
            'user_id': 'yP3ZwJohaxU7No1mPPK1dFiuTIg1',
            'guest_name': 'Salma',
            'start_time': '2025-08-02T20:02:01.973186+00:00',
            'message_count': 23
        },
        {
            'id': 'c85c69af-fdfc-4687-85a1-9e5fa37cfba4',
            'property_id': 'c2d26719-5654-45ee-aa2c-8fbafa637fb4',
            'user_id': 'yP3ZwJohaxU7No1mPPK1dFiuTIg1',
            'guest_name': 'Salma',
            'start_time': '2025-08-02T20:12:06.156962+00:00',
            'message_count': 2
        },
        {
            'id': 'f4a74bcf-2bfa-4f62-906c-3b0b6dd8d913',
            'property_id': 'c2d26719-5654-45ee-aa2c-8fbafa637fb4',
            'user_id': 'yP3ZwJohaxU7No1mPPK1dFiuTIg1',
            'guest_name': 'Salma',
            'start_time': '2025-08-02T20:13:21.885343+00:00',
            'message_count': 5
        }
    ]
    
    print("🎯 ANALYZING PROBLEMATIC CONVERSATIONS FROM AUGUST 2ND")
    print("=" * 80)
    print("These conversations match the criteria:")
    print("- August 2nd, 2025")
    print("- Around 3 PM Chicago time (8 PM UTC)")
    print("- Voice calls")
    print("- User from Pakistan")
    print()
    
    for conv in target_conversations:
        print(f"📞 CONVERSATION: {conv['id']}")
        print(f"   Property: {conv['property_id']}")
        print(f"   User: {conv['user_id']}")
        print(f"   Guest: {conv['guest_name']}")
        print(f"   Start Time: {conv['start_time']}")
        print(f"   Message Count: {conv['message_count']}")
        print()
        
        # Analyze the conversation messages
        conversation = get_conversation_details(
            conv['id'], 
            conv['property_id'], 
            table
        )
        
        if conversation:
            print(f"✅ Successfully analyzed conversation")
        else:
            print(f"❌ Failed to analyze conversation")
        
        print("\n" + "="*80 + "\n")

def main():
    """Main function."""
    analyze_problematic_conversations()

if __name__ == "__main__":
    main() 
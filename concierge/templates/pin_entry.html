{% extends "base.html" %}

{% block title %}Enter PIN - Guestrix{% endblock %}

{% block head %}
<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
<link
  rel="stylesheet"
  as="style"
  onload="this.rel='stylesheet'"
  href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
/>

<style>
    /* Custom color palette */
    :root {
        --persian-green: #2a9d8f;
        --saffron: #e9c46a;
        --dark-purple: #161032;
        --light-cyan: #e0fbfc;
        --bittersweet: #ee6055;
    }

    /* Custom Tailwind configuration */
    .bg-persian-green { background-color: var(--persian-green); }
    .bg-saffron { background-color: var(--saffron); }
    .bg-dark-purple { background-color: var(--dark-purple); }
    .bg-light-cyan { background-color: var(--light-cyan); }
    .bg-bittersweet { background-color: var(--bittersweet); }

    .text-persian-green { color: var(--persian-green); }
    .text-saffron { color: var(--saffron); }
    .text-dark-purple { color: var(--dark-purple); }
    .text-light-cyan { color: var(--light-cyan); }
    .text-bittersweet { color: var(--bittersweet); }

    .border-persian-green { border-color: var(--persian-green); }
    .border-saffron { border-color: var(--saffron); }
    .border-dark-purple { border-color: var(--dark-purple); }
    .border-light-cyan { border-color: var(--light-cyan); }
    .border-bittersweet { border-color: var(--bittersweet); }

    /* Override default body styles */
    body {
        font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif !important;
        background-color: var(--light-cyan) !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Login card styles */
    .login-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(42, 157, 143, 0.15);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
    }

    .login-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 35px rgba(42, 157, 143, 0.2);
    }

    /* Form input styles */
    .form-input {
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 12px 16px;
        font-size: 16px;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
        background-color: white;
        color: var(--dark-purple);
    }

    .form-input:focus {
        outline: none;
        border-color: var(--persian-green);
        box-shadow: 0 0 0 3px rgba(42, 157, 143, 0.1);
    }

    /* Button styles */
    .btn-primary-custom {
        background-color: var(--persian-green);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-primary-custom:hover:not(:disabled) {
        background-color: #238a7a;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(42, 157, 143, 0.3);
    }

    .btn-primary-custom:disabled {
        background-color: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .btn-secondary-custom {
        background-color: #6b7280;
        color: white;
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-secondary-custom:hover {
        background-color: #4b5563;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
        text-decoration: none;
        color: white;
    }

    .btn-link-custom {
        background: none;
        border: none;
        color: var(--persian-green);
        text-decoration: underline;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        padding: 8px;
    }

    .btn-link-custom:hover {
        color: #238a7a;
        text-decoration: underline;
    }

    /* Phone display */
    .phone-display {
        background: rgba(42, 157, 143, 0.1);
        border: 1px solid rgba(42, 157, 143, 0.2);
        border-radius: 12px;
        padding: 12px;
        text-align: center;
        margin-bottom: 24px;
        color: var(--dark-purple);
        font-weight: 600;
    }

    /* PIN input styles */
    .pin-input-container {
        display: flex;
        justify-content: center;
        gap: 12px;
        margin-bottom: 24px;
    }

    .pin-digit {
        width: 48px;
        height: 48px;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        color: var(--dark-purple);
        background-color: white;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .pin-digit:focus {
        outline: none;
        border-color: var(--persian-green);
        box-shadow: 0 0 0 3px rgba(42, 157, 143, 0.1);
    }

    .hidden-input {
        position: absolute;
        left: -9999px;
        opacity: 0;
    }

    /* Alert styles */
    .error-message {
        background-color: var(--bittersweet);
        color: white;
        border-radius: 12px;
        padding: 12px 16px;
        margin-bottom: 20px;
        font-weight: 500;
    }

    .warning-message {
        background-color: var(--saffron);
        color: var(--dark-purple);
        border-radius: 12px;
        padding: 12px 16px;
        margin-bottom: 20px;
        font-weight: 500;
    }

    /* Attempts indicator */
    .attempts-indicator {
        text-align: center;
        margin-bottom: 20px;
    }

    .attempts-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin: 0 4px;
        background: #e5e7eb;
    }

    .attempts-dot.used {
        background: var(--bittersweet);
    }

    /* Hide Bootstrap navbar for login page */
    .navbar {
        display: none !important;
    }

    /* Hide Bootstrap container padding */
    .container.mt-4 {
        margin-top: 0 !important;
        padding: 0 !important;
        max-width: none !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-light-cyan flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Header with Logo -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center gap-3 mb-4">
                <div class="w-12 h-12">
                    <img src="{{ url_for('static', filename='images/guestrix_logo.svg') }}"
                         alt="Guestrix Logo"
                         class="w-full h-full object-contain" />
                </div>
                <h1 class="text-dark-purple text-3xl font-bold">Guestrix</h1>
            </div>
            <p class="text-dark-purple/70 text-lg">Enter your 4-digit PIN to continue</p>
        </div>

        <!-- Login Card -->
        <div class="login-card">
            <!-- Card Header -->
            <div class="bg-persian-green text-white p-6">
                <h2 class="text-xl font-bold mb-0">Enter Your PIN</h2>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <div class="phone-display">
                    📱 {{ phone_number | default('****-****') }}
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            {# Filter out host-specific messages - they're not relevant for guests #}
                            {% set is_host_message = (
                                'knowledge items generated successfully' in message or
                                'knowledge items created' in message or
                                'File uploaded' in message or
                                'Text added' in message or
                                'duplicates skipped' in message or
                                'failed to create knowledge items' in message
                            ) %}
                            {% if not is_host_message %}
                                <div class="{{ 'error-message' if category == 'error' else 'warning-message' }}">
                                    {{ message }}
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Attempt Indicator -->
                {% if attempts > 0 %}
                    <div class="attempts-indicator">
                        <div class="text-dark-purple/60 text-sm mb-2">
                            Failed attempts:
                        </div>
                        {% for i in range(3) %}
                            <span class="attempts-dot {{ 'used' if i < attempts else '' }}"></span>
                        {% endfor %}
                    </div>
                {% endif %}

                <form method="POST" id="pin-form">
                    <!-- Hidden input for form submission -->
                    <input type="text" name="pin" id="pin-hidden" class="hidden-input" maxlength="4">

                    <!-- PIN display -->
                    <div class="pin-input-container">
                        <input type="text" class="pin-digit" maxlength="1" data-index="0" autocomplete="off" inputmode="numeric" pattern="[0-9]*">
                        <input type="text" class="pin-digit" maxlength="1" data-index="1" autocomplete="off" inputmode="numeric" pattern="[0-9]*">
                        <input type="text" class="pin-digit" maxlength="1" data-index="2" autocomplete="off" inputmode="numeric" pattern="[0-9]*">
                        <input type="text" class="pin-digit" maxlength="1" data-index="3" autocomplete="off" inputmode="numeric" pattern="[0-9]*">
                    </div>

                    <button type="submit" class="btn-primary-custom w-full mb-4" id="submit-btn" disabled>
                        Verify PIN
                    </button>
                </form>

                <a href="{{ url_for('auth.phone_login') }}" class="btn-secondary-custom w-full mb-4">
                    ← Use Different Phone Number
                </a>

                <div class="text-center" style="border-top: 1px solid #e5e7eb; padding-top: 20px;">
                    <p class="text-dark-purple/60 text-sm mb-3">
                        Forgot your PIN?
                    </p>
                    <button type="button" class="btn-link-custom" onclick="recoverWithOTP()">
                        Verify with text message instead
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const pinInputs = document.querySelectorAll('.pin-digit');
    const hiddenInput = document.getElementById('pin-hidden');
    const submitBtn = document.getElementById('submit-btn');
    
    // Focus first input
    pinInputs[0].focus();
    
    pinInputs.forEach((input, index) => {
        input.addEventListener('input', function(e) {
            const value = e.target.value;
            
            // Only allow digits
            if (!/^\d*$/.test(value)) {
                e.target.value = '';
                return;
            }
            
            // Update hidden input
            updateHiddenInput();
            
            // Move to next input if value entered
            if (value && index < pinInputs.length - 1) {
                pinInputs[index + 1].focus();
            }
            
            // Enable submit button if all digits entered
            updateSubmitButton();
        });
        
        input.addEventListener('keydown', function(e) {
            // Move to previous input on backspace
            if (e.key === 'Backspace' && !e.target.value && index > 0) {
                pinInputs[index - 1].focus();
            }
            
            // Submit on Enter if PIN is complete
            if (e.key === 'Enter' && getCurrentPin().length === 4) {
                document.getElementById('pin-form').submit();
            }
        });
        
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const paste = e.clipboardData.getData('text');
            const digits = paste.replace(/\D/g, '').slice(0, 4);
            
            // Fill in the digits
            for (let i = 0; i < digits.length && i < pinInputs.length; i++) {
                pinInputs[i].value = digits[i];
            }
            
            updateHiddenInput();
            updateSubmitButton();
            
            // Focus next empty input or last input
            const nextEmpty = Array.from(pinInputs).findIndex(input => !input.value);
            if (nextEmpty !== -1) {
                pinInputs[nextEmpty].focus();
            } else {
                pinInputs[pinInputs.length - 1].focus();
            }
        });
    });
    
    function getCurrentPin() {
        return Array.from(pinInputs).map(input => input.value).join('');
    }
    
    function updateHiddenInput() {
        hiddenInput.value = getCurrentPin();
    }
    
    function updateSubmitButton() {
        const pin = getCurrentPin();
        submitBtn.disabled = pin.length !== 4;
    }
});

function recoverWithOTP() {
    // Redirect to OTP recovery
    window.location.href = '{{ url_for("auth.otp_recovery") }}';
}
</script>
{% endblock %} 
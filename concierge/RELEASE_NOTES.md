# Release Notes

## Version 1.0.1 - 2025-08-04

### Changes
- **PKB-56**: Filter host-specific flash messages from guest-facing templates to prevent confusion
  - Added comprehensive filtering for knowledge management messages in guest authentication flows
  - Affected templates: magic_link_verify, account_type_selection, phone_login, and pin_entry
  - Prevents guests from seeing host-specific notifications about knowledge items, file uploads, etc.

- **PKB-57**: Improved guest authentication experience
  - Hide logout button for temporary users
  - Remove phone login link from magic link verification page
  - Streamlined guest verification flow

- **PKB-60**: Enhanced system prompts
  - Prohibits <PERSON> from promising to take action on guest issues
  - Prevents referring to guests by generic names when no actual name is provided
  - Improved guest context management

- **PKB-52**: Cleaned up Knowledge Base UI
  - Removed unused Bulk Approve button from Knowledge Base tab in Property Management modal
  - Streamlined the knowledge item management interface

- **PKB-51**: Enhanced property setup
  - Added sofabed setup question to property setup modal's Other Information step
  - Improved guest guidance during property onboarding

### Technical Details
- **Branch**: 8-ab-onboarding
- **Files Changed**: 12 files
- **Key Commits**:
  - 84c89ed: PKB-56/PKB-57 - Filter host-specific flash messages
  - 1ec868e: PKB-57 - Hide logout button for temporary users
  - f24e1b4: PKB-60 - System prompt enhancements
  - b3069b3: PKB-52 - Remove Bulk Approve button
  - 651dc8b: PKB-51 - Add sofabed setup question

### Jira Tickets
- **PKB-56**: Filter host-specific notifications from guest views
- **PKB-57**: UI improvements for temporary users and magic link verification
- **PKB-60**: System prompt enhancements for better guest interaction
- **PKB-52**: Remove unused Bulk Approve functionality from Knowledge Base
- **PKB-51**: Property setup modal enhancements for guest guidance

---

## Version History
- **1.0.1 (2025-08-04)**: PKB-51 + PKB-52 + PKB-56 + PKB-57 + PKB-60 - Comprehensive guest experience improvements, system prompt enhancements, and bug fixes